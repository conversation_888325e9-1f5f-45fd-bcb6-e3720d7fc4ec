<?php

namespace App\Filament\Resources;

use App\Filament\Resources\StrategicObjectiveAssignmentResource\Pages;
use App\Filament\Resources\StrategicObjectiveAssignmentResource\RelationManagers;
use App\Models\StrategicObjectiveAssignment;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;

class StrategicObjectiveAssignmentResource extends Resource
{
    protected static ?string $model = StrategicObjectiveAssignment::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getUserOrganizationUnitFirstChild()
{
    $user = Auth::user();

    // Get the employee related to the user
    $employee = $user->employee;

    if (!$employee) {
        return null;
    }

    // Get the organization unit of the employee
    $organizationUnit = $employee->organizationUnit;

    if (!$organizationUnit) {
        return null;
    }

    // Get the first child of the organization unit
    return $organizationUnit->children()->first();

    }



    public static function form(Form $form): Form
    {
        $user = Auth::user();
        $childUnits = collect();

        if ($user && $user->employee && $user->employee->organizationUnit) {
            $childUnits = $user->employee->organizationUnit->children()->where('is_active', true)->get();
        }

        $sections = [];

        // Create a section for each organization unit
        foreach ($childUnits as $index => $unit) {
            $sections[] = Forms\Components\Section::make($unit->name . ' - ' . __('resource.strategic_objective_assignment_resource.sections.assignment'))
                ->description(__('resource.strategic_objective_assignment_resource.sections.assignment_description', ['unit' => $unit->name]))
                ->schema([
                    Forms\Components\Select::make("assignments.{$unit->id}.strategic_objective_id")
                        ->label(__('resource.strategic_objective_assignment_resource.fields.strategic_objective'))
                        ->relationship('strategicObjective', 'strategic_objective')
                        ->searchable()
                        ->preload()
                        ->required()
                        ->live()
                        ->afterStateUpdated(function ($state, Forms\Set $set) use ($unit) {
                            if ($state) {
                                $strategicObjective = \App\Models\StrategicObjective::find($state);
                                if ($strategicObjective) {
                                    $set("assignments.{$unit->id}.strategic_objective_weight", $strategicObjective->weight);
                                }
                            } else {
                                $set("assignments.{$unit->id}.strategic_objective_weight", null);
                            }
                        }),

                    Forms\Components\TextInput::make("assignments.{$unit->id}.strategic_objective_weight")
                        ->label(__('resource.strategic_objective_assignment_resource.fields.strategic_objective_weight'))
                        ->disabled()
                        ->dehydrated(false)
                        ->suffix('%')
                        ->numeric()
                        ->placeholder(__('resource.strategic_objective_assignment_resource.placeholders.auto_filled')),

                    Forms\Components\TextInput::make("assignments.{$unit->id}.organization_unit_name")
                        ->label(__('resource.strategic_objective_assignment_resource.fields.organization_unit'))
                        ->default($unit->name)
                        ->disabled()
                        ->dehydrated(false),

                    Forms\Components\TextInput::make("assignments.{$unit->id}.assignment_weight")
                        ->label(__('resource.strategic_objective_assignment_resource.fields.assignment_weight'))
                        ->numeric()
                        ->step(0.01)
                        ->minValue(0)
                        ->maxValue(100)
                        ->suffix('%')
                        ->placeholder('0.00')
                        ->required()
                        ->helperText(__('resource.strategic_objective_assignment_resource.fields.weight_helper')),

                    Forms\Components\Toggle::make("assignments.{$unit->id}.is_active")
                        ->label(__('resource.strategic_objective_assignment_resource.fields.is_active'))
                        ->default(true)
                        ->inline(false),

                    // Hidden field to store organization unit ID
                    Forms\Components\Hidden::make("assignments.{$unit->id}.organization_unit_id")
                        ->default($unit->id),
                ])
                ->columns(2)
                ->collapsible()
                ->collapsed($index > 0); // Collapse all except the first one
        }

        // Add summary section if there are child units
        if ($childUnits->count() > 0) {
            $sections[] = Forms\Components\Section::make(__('resource.strategic_objective_assignment_resource.sections.summary'))
                ->description(__('resource.strategic_objective_assignment_resource.sections.summary_description'))
                ->schema([
                    Forms\Components\Placeholder::make('total_assignments')
                        ->label(__('resource.strategic_objective_assignment_resource.fields.total_assignments'))
                        ->content(function (Forms\Get $get) use ($childUnits) {
                            $assignments = $get('assignments') ?? [];
                            $activeCount = 0;
                            foreach ($childUnits as $unit) {
                                if (isset($assignments[$unit->id]['is_active']) && $assignments[$unit->id]['is_active']) {
                                    $activeCount++;
                                }
                            }
                            return "{$activeCount} / {$childUnits->count()}";
                        }),

                    Forms\Components\Placeholder::make('total_weight')
                        ->label(__('resource.strategic_objective_assignment_resource.fields.total_weight'))
                        ->content(function (Forms\Get $get) use ($childUnits) {
                            $assignments = $get('assignments') ?? [];
                            $totalWeight = 0;
                            foreach ($childUnits as $unit) {
                                if (isset($assignments[$unit->id]['assignment_weight']) && is_numeric($assignments[$unit->id]['assignment_weight'])) {
                                    $totalWeight += (float) $assignments[$unit->id]['assignment_weight'];
                                }
                            }
                            $color = $totalWeight == 100 ? 'success' : ($totalWeight > 100 ? 'danger' : 'warning');
                            return new \Illuminate\Support\HtmlString(
                                "<span class='text-{$color}-600 font-semibold'>{$totalWeight}%</span>"
                            );
                        }),
                ])
                ->columns(2);
        }

        // If no child units, show a message
        if ($childUnits->count() === 0) {
            $sections[] = Forms\Components\Section::make(__('resource.strategic_objective_assignment_resource.sections.no_units'))
                ->description(__('resource.strategic_objective_assignment_resource.sections.no_units_description'))
                ->schema([
                    Forms\Components\Placeholder::make('no_units_message')
                        ->content(__('resource.strategic_objective_assignment_resource.messages.no_child_units')),
                ]);
        }

        return $form->schema($sections);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('resource.strategic_objective_assignment_resource.fields.id'))
                    ->sortable(),

                Tables\Columns\TextColumn::make('strategicObjective.strategic_objective')
                    ->label(__('resource.strategic_objective_assignment_resource.fields.strategic_objective_id'))
                    ->searchable()
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),

                Tables\Columns\TextColumn::make('strategicObjective.weight')
                    ->label(__('resource.strategic_objective_assignment_resource.fields.strategic_objective_weight'))
                    ->suffix('%')
                    ->sortable(),

                Tables\Columns\TextColumn::make('organizationUnit.name')
                    ->label(__('resource.strategic_objective_assignment_resource.fields.organization_unit_id'))
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('weight')
                    ->label(__('resource.strategic_objective_assignment_resource.fields.assignment_weight'))
                    ->suffix('%')
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label(__('resource.strategic_objective_assignment_resource.fields.is_active'))
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('resource.strategic_objective_assignment_resource.fields.created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('resource.strategic_objective_assignment_resource.fields.updated_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('strategic_objective_id')
                    ->label(__('resource.strategic_objective_assignment_resource.fields.strategic_objective_id'))
                    ->relationship('strategicObjective', 'strategic_objective')
                    ->multiple()
                    ->preload(),

                Tables\Filters\SelectFilter::make('organization_unit_id')
                    ->label(__('resource.strategic_objective_assignment_resource.fields.organization_unit_id'))
                    ->relationship('organizationUnit', 'name')
                    ->multiple()
                    ->preload(),

                Tables\Filters\Filter::make('is_active')
                    ->label(__('resource.strategic_objective_assignment_resource.filters.active'))
                    ->query(fn (Builder $query): Builder => $query->where('is_active', true)),

                Tables\Filters\Filter::make('weight_range')
                    ->form([
                        Forms\Components\TextInput::make('min_weight')
                            ->label(__('resource.strategic_objective_assignment_resource.fields.min_weight'))
                            ->numeric(),
                        Forms\Components\TextInput::make('max_weight')
                            ->label(__('resource.strategic_objective_assignment_resource.fields.max_weight'))
                            ->numeric(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['min_weight'],
                                fn (Builder $query, $weight): Builder => $query->where('weight', '>=', $weight),
                            )
                            ->when(
                                $data['max_weight'],
                                fn (Builder $query, $weight): Builder => $query->where('weight', '<=', $weight),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->label(__('resource.strategic_objective_assignment_resource.actions.activate'))
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->action(function ($records) {
                            $records->each(function ($record) {
                                $record->update(['is_active' => true]);
                            });
                        })
                        ->deselectRecordsAfterCompletion(),

                    Tables\Actions\BulkAction::make('deactivate')
                        ->label(__('resource.strategic_objective_assignment_resource.actions.deactivate'))
                        ->icon('heroicon-o-x-mark')
                        ->color('danger')
                        ->action(function ($records) {
                            $records->each(function ($record) {
                                $record->update(['is_active' => false]);
                            });
                        })
                        ->deselectRecordsAfterCompletion(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStrategicObjectiveAssignments::route('/'),
            'create' => Pages\CreateStrategicObjectiveAssignment::route('/create'),
            'edit' => Pages\EditStrategicObjectiveAssignment::route('/{record}/edit'),
        ];
    }
}
