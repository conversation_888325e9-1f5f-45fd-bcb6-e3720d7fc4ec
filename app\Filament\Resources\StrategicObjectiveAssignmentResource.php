<?php

namespace App\Filament\Resources;

use App\Filament\Resources\StrategicObjectiveAssignmentResource\Pages;
use App\Filament\Resources\StrategicObjectiveAssignmentResource\RelationManagers;
use App\Models\StrategicObjectiveAssignment;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;

class StrategicObjectiveAssignmentResource extends Resource
{
    protected static ?string $model = StrategicObjectiveAssignment::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getUserOrganizationUnitFirstChild()
{
    $user = Auth::user();

    // Get the employee related to the user
    $employee = $user->employee;

    if (!$employee) {
        return null;
    }

    // Get the organization unit of the employee
    $organizationUnit = $employee->organizationUnit;

    if (!$organizationUnit) {
        return null;
    }

    // Get the first child of the organization unit
    return $organizationUnit->children()->first();

    }



    public static function form(Form $form): Form
    {
        $user = Auth::user();
        $childUnits = collect();

        if ($user && $user->employee && $user->employee->organizationUnit) {
            $childUnits = $user->employee->organizationUnit->children()->where('is_active', true)->get();
        }

        return $form
            ->schema([
                Forms\Components\Section::make(__('resource.strategic_objective_assignment_resource.sections.strategic_objective'))
                    ->schema([
                        Forms\Components\Select::make('strategic_objective_id')
                            ->label(__('resource.strategic_objective_assignment_resource.fields.strategic_objective_id'))
                            ->relationship('strategicObjective', 'strategic_objective')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                if ($state) {
                                    $strategicObjective = \App\Models\StrategicObjective::find($state);
                                    if ($strategicObjective) {
                                        $set('strategic_objective_weight', $strategicObjective->weight);
                                        $set('strategic_objective_name', $strategicObjective->strategic_objective);
                                    }
                                } else {
                                    $set('strategic_objective_weight', null);
                                    $set('strategic_objective_name', null);
                                }
                            }),

                        Forms\Components\TextInput::make('strategic_objective_weight')
                            ->label(__('resource.strategic_objective_assignment_resource.fields.strategic_objective_weight'))
                            ->disabled()
                            ->dehydrated(false)
                            ->suffix('%')
                            ->numeric(),

                        Forms\Components\Textarea::make('strategic_objective_name')
                            ->label(__('resource.strategic_objective_assignment_resource.fields.strategic_objective_name'))
                            ->disabled()
                            ->dehydrated(false)
                            ->rows(2),
                    ])->columns(2),

                Forms\Components\Section::make(__('resource.strategic_objective_assignment_resource.sections.organization_units'))
                    ->description(__('resource.strategic_objective_assignment_resource.sections.organization_units_description'))
                    ->schema(
                        $childUnits->map(function ($unit) {
                            return Forms\Components\Group::make([
                                Forms\Components\TextInput::make('unit_name_' . $unit->id)
                                    ->label(__('resource.strategic_objective_assignment_resource.fields.organization_unit'))
                                    ->default($unit->name)
                                    ->disabled()
                                    ->dehydrated(false),

                                Forms\Components\TextInput::make('weights.' . $unit->id)
                                    ->label(__('resource.strategic_objective_assignment_resource.fields.assignment_weight'))
                                    ->numeric()
                                    ->step(0.01)
                                    ->minValue(0)
                                    ->maxValue(100)
                                    ->suffix('%')
                                    ->placeholder('0.00')
                                    ->helperText(__('resource.strategic_objective_assignment_resource.fields.weight_helper')),

                                Forms\Components\Toggle::make('active.' . $unit->id)
                                    ->label(__('resource.strategic_objective_assignment_resource.fields.is_active'))
                                    ->default(true)
                                    ->inline(false),
                            ])->columns(3);
                        })->toArray()
                    )
                    ->visible($childUnits->count() > 0)
                    ->collapsible(),

                Forms\Components\Section::make(__('resource.strategic_objective_assignment_resource.sections.summary'))
                    ->schema([
                        Forms\Components\Placeholder::make('total_weight')
                            ->label(__('resource.strategic_objective_assignment_resource.fields.total_weight'))
                            ->content(function (Forms\Get $get) use ($childUnits) {
                                $weights = $get('weights') ?? [];
                                $total = array_sum(array_filter($weights, 'is_numeric'));
                                $color = $total == 100 ? 'success' : ($total > 100 ? 'danger' : 'warning');
                                return new \Illuminate\Support\HtmlString(
                                    "<span class='text-{$color}-600 font-semibold'>{$total}%</span>"
                                );
                            }),

                        Forms\Components\Placeholder::make('validation_message')
                            ->label('')
                            ->content(function (Forms\Get $get) use ($childUnits) {
                                $weights = $get('weights') ?? [];
                                $total = array_sum(array_filter($weights, 'is_numeric'));

                                if ($total == 100) {
                                    return new \Illuminate\Support\HtmlString(
                                        "<span class='text-success-600'>✓ " . __('resource.strategic_objective_assignment_resource.messages.weight_perfect') . "</span>"
                                    );
                                } elseif ($total > 100) {
                                    return new \Illuminate\Support\HtmlString(
                                        "<span class='text-danger-600'>⚠ " . __('resource.strategic_objective_assignment_resource.messages.weight_over') . "</span>"
                                    );
                                } else {
                                    return new \Illuminate\Support\HtmlString(
                                        "<span class='text-warning-600'>⚠ " . __('resource.strategic_objective_assignment_resource.messages.weight_under') . "</span>"
                                    );
                                }
                            }),
                    ])->columns(2)
                    ->visible($childUnits->count() > 0),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('resource.strategic_objective_assignment_resource.fields.id'))
                    ->sortable(),

                Tables\Columns\TextColumn::make('strategicObjective.strategic_objective')
                    ->label(__('resource.strategic_objective_assignment_resource.fields.strategic_objective_id'))
                    ->searchable()
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),

                Tables\Columns\TextColumn::make('strategicObjective.weight')
                    ->label(__('resource.strategic_objective_assignment_resource.fields.strategic_objective_weight'))
                    ->suffix('%')
                    ->sortable(),

                Tables\Columns\TextColumn::make('organizationUnit.name')
                    ->label(__('resource.strategic_objective_assignment_resource.fields.organization_unit_id'))
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('weight')
                    ->label(__('resource.strategic_objective_assignment_resource.fields.assignment_weight'))
                    ->suffix('%')
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label(__('resource.strategic_objective_assignment_resource.fields.is_active'))
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('resource.strategic_objective_assignment_resource.fields.created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('resource.strategic_objective_assignment_resource.fields.updated_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('strategic_objective_id')
                    ->label(__('resource.strategic_objective_assignment_resource.fields.strategic_objective_id'))
                    ->relationship('strategicObjective', 'strategic_objective')
                    ->multiple()
                    ->preload(),

                Tables\Filters\SelectFilter::make('organization_unit_id')
                    ->label(__('resource.strategic_objective_assignment_resource.fields.organization_unit_id'))
                    ->relationship('organizationUnit', 'name')
                    ->multiple()
                    ->preload(),

                Tables\Filters\Filter::make('is_active')
                    ->label(__('resource.strategic_objective_assignment_resource.filters.active'))
                    ->query(fn (Builder $query): Builder => $query->where('is_active', true)),

                Tables\Filters\Filter::make('weight_range')
                    ->form([
                        Forms\Components\TextInput::make('min_weight')
                            ->label(__('resource.strategic_objective_assignment_resource.fields.min_weight'))
                            ->numeric(),
                        Forms\Components\TextInput::make('max_weight')
                            ->label(__('resource.strategic_objective_assignment_resource.fields.max_weight'))
                            ->numeric(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['min_weight'],
                                fn (Builder $query, $weight): Builder => $query->where('weight', '>=', $weight),
                            )
                            ->when(
                                $data['max_weight'],
                                fn (Builder $query, $weight): Builder => $query->where('weight', '<=', $weight),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->label(__('resource.strategic_objective_assignment_resource.actions.activate'))
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->action(function ($records) {
                            $records->each(function ($record) {
                                $record->update(['is_active' => true]);
                            });
                        })
                        ->deselectRecordsAfterCompletion(),

                    Tables\Actions\BulkAction::make('deactivate')
                        ->label(__('resource.strategic_objective_assignment_resource.actions.deactivate'))
                        ->icon('heroicon-o-x-mark')
                        ->color('danger')
                        ->action(function ($records) {
                            $records->each(function ($record) {
                                $record->update(['is_active' => false]);
                            });
                        })
                        ->deselectRecordsAfterCompletion(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStrategicObjectiveAssignments::route('/'),
            'create' => Pages\CreateStrategicObjectiveAssignment::route('/create'),
            'edit' => Pages\EditStrategicObjectiveAssignment::route('/{record}/edit'),
        ];
    }
}
