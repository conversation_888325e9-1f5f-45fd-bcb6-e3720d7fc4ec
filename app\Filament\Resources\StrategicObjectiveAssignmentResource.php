<?php

namespace App\Filament\Resources;

use App\Filament\Resources\StrategicObjectiveAssignmentResource\Pages;
use App\Filament\Resources\StrategicObjectiveAssignmentResource\RelationManagers;
use App\Models\StrategicObjectiveAssignment;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;

class StrategicObjectiveAssignmentResource extends Resource
{
    protected static ?string $model = StrategicObjectiveAssignment::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        $user = Auth::user();
        $childUnits = collect();

        if ($user && $user->employee && $user->employee->organizationUnit) {
            $childUnits = $user->employee->organizationUnit->children()->get();
        }

        $tabs = [];

        // Create a tab for each organization unit with its own form
        foreach ($childUnits as $unit) {
            $tabs[] = Forms\Components\Tabs\Tab::make($unit->name)
                ->schema([
                    Forms\Components\Section::make(__('resource.strategic_objective_assignment_resource.sections.assignment_form'))
                        ->description(__('resource.strategic_objective_assignment_resource.sections.assignment_form_description', ['unit' => $unit->name]))
                        ->schema([
                            Forms\Components\Select::make("unit_{$unit->id}_strategic_objective_id")
                                ->label(__('resource.strategic_objective_assignment_resource.fields.strategic_objective'))
                                ->options(\App\Models\StrategicObjective::where('is_active', true)->pluck('strategic_objective', 'id'))
                                ->searchable()
                                ->preload()
                                ->required()
                                ->live()
                                ->afterStateUpdated(function ($state, Forms\Set $set) use ($unit) {
                                    if ($state) {
                                        $strategicObjective = \App\Models\StrategicObjective::find($state);
                                        if ($strategicObjective) {
                                            $set("unit_{$unit->id}_strategic_objective_weight", $strategicObjective->weight);
                                        }
                                    } else {
                                        $set("unit_{$unit->id}_strategic_objective_weight", null);
                                    }
                                }),

                            Forms\Components\TextInput::make("unit_{$unit->id}_strategic_objective_weight")
                                ->label(__('resource.strategic_objective_assignment_resource.fields.strategic_objective_weight'))
                                ->disabled()
                                ->dehydrated(false)
                                ->suffix('%')
                                ->numeric(),
                                // ->placeholder(__('resource.strategic_objective_assignment_resource.placeholders.auto_filled')),

                            Forms\Components\TextInput::make("unit_{$unit->id}_organization_unit_name")
                                ->label(__('resource.strategic_objective_assignment_resource.fields.organization_unit'))
                                ->default($unit->name)
                                ->disabled()
                                ->dehydrated(false),

                            Forms\Components\TextInput::make("unit_{$unit->id}_assignment_weight")
                                ->label(__('resource.strategic_objective_assignment_resource.fields.assignment_weight'))
                                ->numeric()
                                ->step(0.01)
                                ->minValue(0)
                                ->maxValue(100)
                                ->suffix('%')
                                ->placeholder('0.00')
                                ->required(),
                                // ->helperText(__('resource.strategic_objective_assignment_resource.fields.weight_helper')),

                            Forms\Components\Toggle::make("unit_{$unit->id}_is_active")
                                ->label(__('resource.strategic_objective_assignment_resource.fields.is_active'))
                                ->default(true)
                                ->inline(false),

                            // Hidden field to store organization unit ID
                            Forms\Components\Hidden::make("unit_{$unit->id}_organization_unit_id")
                                ->default($unit->id),
                        ])
                        ->columns(2),

                    // Create button for this specific unit
                    Forms\Components\Actions::make([
                        Forms\Components\Actions\Action::make("create_assignment_{$unit->id}")
                            ->label(__('resource.strategic_objective_assignment_resource.actions.create_assignment', ['unit' => $unit->name]))
                            ->icon('heroicon-o-plus-circle')
                            ->color('success')
                            ->size('lg')
                            ->action(function (array $data, $livewire) use ($unit) {
                                // Extract data for this specific unit
                                $strategicObjectiveId = $data["unit_{$unit->id}_strategic_objective_id"] ?? null;
                                $assignmentWeight = $data["unit_{$unit->id}_assignment_weight"] ?? null;
                                $isActive = $data["unit_{$unit->id}_is_active"] ?? true;

                                // Validate required fields
                                if (empty($strategicObjectiveId) || empty($assignmentWeight)) {
                                    \Filament\Notifications\Notification::make()
                                        ->title(__('resource.strategic_objective_assignment_resource.notifications.validation_error'))
                                        ->body(__('resource.strategic_objective_assignment_resource.notifications.required_fields_missing'))
                                        ->danger()
                                        ->send();
                                    return;
                                }

                                // Check if assignment already exists
                                $existingAssignment = \App\Models\StrategicObjectiveAssignment::where([
                                    'strategic_objective_id' => $strategicObjectiveId,
                                    'organization_unit_id' => $unit->id,
                                ])->first();

                                if ($existingAssignment) {
                                    \Filament\Notifications\Notification::make()
                                        ->title(__('resource.strategic_objective_assignment_resource.notifications.assignment_exists'))
                                        // ->body(__('resource.strategic_objective_assignment_resource.notifications.assignment_exists_description', ['unit' => $unit->name]))
                                        ->warning()
                                        ->send();
                                    return;
                                }

                                try {
                                    // Create new assignment
                                    \App\Models\StrategicObjectiveAssignment::create([
                                        'strategic_objective_id' => $strategicObjectiveId,
                                        'organization_unit_id' => $unit->id,
                                        'weight' => $assignmentWeight,
                                        'is_active' => $isActive,
                                    ]);

                                    \Filament\Notifications\Notification::make()
                                        ->title(__('resource.strategic_objective_assignment_resource.notifications.created_success'))
                                        ->body(__('resource.strategic_objective_assignment_resource.notifications.created_success_description', ['unit' => $unit->name]))
                                        ->success()
                                        ->send();

                                    // Clear the form fields for this unit
                                    $livewire->form->fill([
                                        "unit_{$unit->id}_strategic_objective_id" => null,
                                        "unit_{$unit->id}_strategic_objective_weight" => null,
                                        "unit_{$unit->id}_assignment_weight" => null,
                                        "unit_{$unit->id}_is_active" => true,
                                    ]);

                                } catch (\Exception $e) {
                                    \Filament\Notifications\Notification::make()
                                        ->title(__('resource.strategic_objective_assignment_resource.notifications.creation_error'))
                                        ->body(__('resource.strategic_objective_assignment_resource.notifications.creation_error_description'))
                                        ->danger()
                                        ->send();
                                }
                            })
                            ->requiresConfirmation()
                            ->modalHeading(__('resource.strategic_objective_assignment_resource.modals.confirm_create_title', ['unit' => $unit->name]))
                            ->modalDescription(__('resource.strategic_objective_assignment_resource.modals.confirm_create_description'))
                            ->modalSubmitActionLabel(__('resource.strategic_objective_assignment_resource.actions.confirm_create'))
                    ])
                ]);
        }

        // If no child units, show a message
        if ($childUnits->count() === 0) {
            return $form->schema([
                Forms\Components\Section::make(__('resource.strategic_objective_assignment_resource.sections.no_units'))
                    ->description(__('resource.strategic_objective_assignment_resource.sections.no_units_description'))
                    ->schema([
                        Forms\Components\Placeholder::make('no_units_message')
                            ->content(__('resource.strategic_objective_assignment_resource.messages.no_child_units')),
                    ]),
            ]);
        }

        return $form->schema([
            Forms\Components\Tabs::make('organization_units_tabs')
                ->tabs($tabs)
                ->activeTab(1)
                ->persistTabInQueryString()
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([

            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStrategicObjectiveAssignments::route('/'),
            'create' => Pages\CreateStrategicObjectiveAssignment::route('/create'),
            'edit' => Pages\EditStrategicObjectiveAssignment::route('/{record}/edit'),
        ];
    }
}

