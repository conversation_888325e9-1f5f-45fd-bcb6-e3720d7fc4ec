<?php

namespace App\Filament\Resources;

use App\Filament\Resources\StrategicObjectiveAssignmentResource\Pages;
use App\Filament\Resources\StrategicObjectiveAssignmentResource\RelationManagers;
use App\Models\StrategicObjectiveAssignment;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;

class StrategicObjectiveAssignmentResource extends Resource
{
    protected static ?string $model = StrategicObjectiveAssignment::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getUserOrganizationUnitFirstChild()
{
    $user = Auth::user();

    // Get the employee related to the user
    $employee = $user->employee;

    if (!$employee) {
        return null;
    }

    // Get the organization unit of the employee
    $organizationUnit = $employee->organizationUnit;

    if (!$organizationUnit) {
        return null;
    }

    // Get the first child of the organization unit
    return $organizationUnit->children()->first();

    }



    public static function form(Form $form): Form
    {

        $user = Auth::user();
        $childUnits = [];

        if ($user && $user->employee && $user->employee->organizationUnit) {
        $childUnits = $user->employee->organizationUnit->children()->get();
        }
        return $form
            ->schema([
                Forms\Components\Select::make('strategic_objective_id')
                    ->label(__('resource.strategic_objective_assignment_resource.fields.strategic_objective_id'))
                    ->relationship('strategicObjective', 'strategic_objective')
                    ->searchable()
                    ->preload()
                    ->live()
                    ->afterStateUpdated(function ($state, Forms\Set $set) {
                        if ($state) {
                            $strategicObjective = \App\Models\StrategicObjective::find($state);
                            if ($strategicObjective) {
                                $set('weight', $strategicObjective->weight);
                            }
                        } else {
                            $set('weight', null);
                        }
                    }),
                Forms\Components\TextInput::make('weight')
                    ->label(__('resource.strategic_objective_assignment_resource.fields.weight'))
                    ->disabled()
                    ->dehydrated(false),

                Forms\Components\Section::make(__('resource.strategic_objective_assignment_resource.fields.organization_unit_id'))
                    ->schema(
                        $childUnits->map(function ($unit) {
                            return Forms\Components\TextInput::make('weights.' . $unit->id)
                                ->label($unit->name);
                        })->toArray()
                    )->columns(2)
                    ->visible(count($childUnits) > 0),


            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                //
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStrategicObjectiveAssignments::route('/'),
            'create' => Pages\CreateStrategicObjectiveAssignment::route('/create'),
            'edit' => Pages\EditStrategicObjectiveAssignment::route('/{record}/edit'),
        ];
    }
}
