<?php

namespace App\Filament\Resources\StrategicObjectiveAssignmentResource\Pages;

use App\Filament\Resources\StrategicObjectiveAssignmentResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditStrategicObjectiveAssignment extends EditRecord
{
    protected static string $resource = StrategicObjectiveAssignmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
