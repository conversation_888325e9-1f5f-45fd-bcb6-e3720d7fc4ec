<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Employee extends Model
{
    protected $fillable = [
        'name',
        'sex',
        'job_position_date',
        'job_position_id',
        'organization_unit_id',
        'job_type_id',
        'is_active',
    ];

    protected $casts = [
        'name' => 'array',
        'sex' => 'array',
    ];

    public $translatable = ['name', 'sex'];

    public function jobPosition()
    {
        return $this->belongsTo(JobPosition::class);
    }

    public function organizationUnit()
    {
        return $this->belongsTo(OrganizationUnit::class);
    }

    public function jobType()
    {
        return $this->belongsTo(JobType::class);
    }
}
