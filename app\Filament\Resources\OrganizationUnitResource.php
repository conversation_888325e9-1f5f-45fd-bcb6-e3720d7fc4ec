<?php

namespace App\Filament\Resources;

use App\Filament\Resources\OrganizationUnitResource\Pages;
use App\Filament\Resources\OrganizationUnitResource\RelationManagers;
use App\Models\OrganizationUnit;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class OrganizationUnitResource extends Resource
{
    protected static ?string $model = OrganizationUnit::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')->label(__('resource.organization_unit_resource.fields.name')),
                Forms\Components\TextInput::make('accronym')->label(__('resource.organization_unit_resource.fields.accronym')),
                Forms\Components\Select::make('unit_type_id')->relationship('unitType', 'name')->label(__('resource.organization_unit_resource.fields.unit_type_id')),
                Forms\Components\Select::make('parent_id')->relationship('parent', 'name')->label(__('resource.organization_unit_resource.fields.parent_id')),
                Forms\Components\Toggle::make('is_active')->label(__('resource.organization_unit_resource.fields.is_active')),
                Forms\Components\Toggle::make('is_root')->label(__('resource.organization_unit_resource.fields.is_root')),
                Forms\Components\Toggle::make('is_last_child')->label(__('resource.organization_unit_resource.fields.is_last_child')),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->label(__('resource.organization_unit_resource.fields.name')),
                Tables\Columns\TextColumn::make('accronym')->label(__('resource.organization_unit_resource.fields.accronym')),
                Tables\Columns\TextColumn::make('unitType.name')->label(__('resource.organization_unit_resource.fields.unit_type_id')),
                Tables\Columns\TextColumn::make('parent.name')->label(__('resource.organization_unit_resource.fields.parent_id')),
                Tables\Columns\IconColumn::make('is_active')->label(__('resource.organization_unit_resource.fields.is_active'))
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                Tables\Columns\IconColumn::make('is_root')->label(__('resource.organization_unit_resource.fields.is_root'))
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                Tables\Columns\IconColumn::make('is_last_child')->label(__('resource.organization_unit_resource.fields.is_last_child'))
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrganizationUnits::route('/'),
            'create' => Pages\CreateOrganizationUnit::route('/create'),
            'edit' => Pages\EditOrganizationUnit::route('/{record}/edit'),
        ];
    }
}
