<?php

namespace App\Filament\Resources;

use App\Filament\Resources\QuarterResource\Pages;
use App\Filament\Resources\QuarterResource\RelationManagers;
use App\Models\Quarter;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class QuarterResource extends Resource
{
    use Translatable;
    protected static ?string $model = Quarter::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = null;

    public static function getNavigationGroup(): ?string
    {
        return __('resource.main.plan_group');
    }

    public static function getNavigationBadge(): ?string
    {
        return strval(static::getEloquentQuery()->count());
    }

    public static function getNavigationLabel(): string
    {
        return __('resource.quarter_resource.label');
    }

    public static function getLabel(): string
    {
        return __('resource.quarter_resource.label');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')->label(__('resource.quarter_resource.fields.name')),
                Forms\Components\Select::make('half_year_id')->relationship('halfYear', 'name')->label(__('resource.quarter_resource.fields.half_year_id'))->searchable()->preload(),
                Forms\Components\DatePicker::make('start_date')->label(__('resource.quarter_resource.fields.start_date')),
                Forms\Components\DatePicker::make('end_date')->label(__('resource.quarter_resource.fields.end_date')),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->label(__('resource.quarter_resource.fields.name')),
                Tables\Columns\TextColumn::make('halfYear.name')->label(__('resource.quarter_resource.fields.half_year_id')),
                Tables\Columns\TextColumn::make('start_date')->label(__('resource.quarter_resource.fields.start_date')),
                Tables\Columns\TextColumn::make('end_date')->label(__('resource.quarter_resource.fields.end_date')),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListQuarters::route('/'),
            'create' => Pages\CreateQuarter::route('/create'),
            'edit' => Pages\EditQuarter::route('/{record}/edit'),
        ];
    }
}
