<?php

namespace App\Filament\Resources\StrategicObjectiveAssignmentResource\Pages;

use App\Filament\Resources\StrategicObjectiveAssignmentResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateStrategicObjectiveAssignment extends CreateRecord
{
    protected static string $resource = StrategicObjectiveAssignmentResource::class;

    // In CreateStrategicObjectiveAssignment.php
    public function create(bool $another = false): void
    {
        $data = $this->form->getState();

        if (isset($data['assignments'])) {
            foreach ($data['assignments'] as $unitId => $assignment) {
                if (isset($assignment['strategic_objective_id']) && isset($assignment['assignment_weight'])) {
                    $this->getModel()::create([
                        'strategic_objective_id' => $assignment['strategic_objective_id'],
                        'organization_unit_id' => $assignment['organization_unit_id'] ?? $unitId,
                        'weight' => $assignment['assignment_weight'],
                        'is_active' => $assignment['is_active'] ?? true,
                    ]);
                }
            }
        }

        $this->redirect($this->getResource()::getUrl('index'));
    }
}
