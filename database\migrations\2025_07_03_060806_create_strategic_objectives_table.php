<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('strategic_objectives', function (Blueprint $table) {
            $table->id();
            $table->json('strategic_objective');
            $table->json('description');
            $table->unsignedBigInteger('perspective_id');
            $table->foreign('perspective_id')->references('id')->on('perspectives')->onDelete('cascade');
            $table->decimal('weight', 5, 2)->nullable();
            $table->integer('number_of_year')->nullable();
            $table->date('start_date')->nullable();
            $table->text('strategy_actions')->nullable();
            $table->date('end_date')->nullable();
            $table->text('expected_results')->nullable();
            $table->boolian('is_approved')->default(false);
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->foreign('approved_by')->references('id')->on('employees')->onDelete('cascade');
            $table->unsignedBigInteger('employee_id')->nullable();
            $table->foreign('employee_id')->references('id')->on('employees')->onDelete('cascade');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('strategic_objectives');
    }
};
