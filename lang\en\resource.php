<?php

return [

    'main' => [
        'dashboard' => 'Dashboard',
        'organo_structure_group' => 'Organization Structure',
        'role_manager_group' => 'Role Manager',
        'plan_group' => 'Plan',
    ],

    'role resource' => [
        'fields' => [
            'name' => 'Name',
            'description' => 'Description',
            'guard_name' => 'Guard Name',
            'permissions_count' => 'Permissions',
            'updated_at' => 'Updated At',
        ],

        'label' => 'Roles',
        'create' => 'Create Role',
        'edit' => 'Edit Role',
        'view' => 'View Role',
    ],

    'user resource' => [
        'fields' => [
            'name' => 'Name',
            'email' => 'Email',
            'email_verified_at' => 'Email Verified At',
            'password' => 'Password',
            'roles' => 'Roles',
            'id' => 'ID',
        ],

        'label' => 'Users',
    ],

    'unit_type_resource' => [
        'fields' => [
            'name' => 'Name',
            'hirarchy_level' => 'Hirarchy Level',
        ],

        'label' => 'Unit Types',
        'create' => 'Create Unit Type',
        'edit' => 'Edit Unit Type',
        'view' => 'View Unit Type',
    ],

    'organization_unit_resource' => [
        'fields' => [
            'name' => 'Name',
            'accronym' => 'Acronym',
            'unit_type_id' => 'Unit Type',
            'unit_type_id_placeholder' => 'Select Unit Type',
            'parent_id' => 'Parent',
            'parent_id_placeholder' => 'Select Parent',
            'is_active' => 'Is Active',
            'is_root' => 'Is Root',
            'is_last_child' => 'Is Last Child',
        ],

        'label' => 'Organization Units',
        'create' => 'Create Organization Unit',
        'edit' => 'Edit Organization Unit',
        'view' => 'View Organization Unit',
    ],

    'job_title_category_resource' => [
        'fields' => [
            'name' => 'Name',
            'description' => 'Description',
            'parent_id' => 'Parent',
            'parent_id_placeholder' => 'Select Parent',
            'code' => 'Code',
            'is_active' => 'Is Active',
        ],

        'label' => 'Job Title Categories',
        'create' => 'Create Job Title Category',
        'edit' => 'Edit Job Title Category',
        'view' => 'View Job Title Category',
    ],

    'job_type_resource' => [
        'fields' => [
            'name' => 'Name',
            'description' => 'Description',
        ],

        'label' => 'Job Types',
        'create' => 'Create Job Type',
        'edit' => 'Edit Job Type',
        'view' => 'View Job Type',
    ],

    'job_position_resource' => [
        'fields' => [
            'name' => 'Name',
            'description' => 'Description',
            'parent_id' => 'Parent',
            'parent_id_placeholder' => 'Select Parent',
            'job_title_category_id' => 'Job Title Category',
            'job_title_category_id_placeholder' => 'Select Job Title Category',
            'job_type_id' => 'Job Type',
            'job_type_id_placeholder' => 'Select Job Type',
            'organization_unit_id' => 'Organization Unit',
            'organization_unit_id_placeholder' => 'Select Organization Unit',
            'is_active' => 'Is Active',
        ],

        'label' => 'Job Positions',
        'create' => 'Create Job Position',
        'edit' => 'Edit Job Position',
        'view' => 'View Job Position',
    ],

    'employee_resource' => [
        'fields' => [
            'name' => 'Name',
            'sex' => 'Sex',
            'sex_male' => 'Male',
            'sex_female' => 'Female',
            'job_position_date' => 'Job Position Date',
            'job_position_id' => 'Job Position',
            'job_position_id_placeholder' => 'Select Job Position',
            'organization_unit_id' => 'Organization Unit',
            'organization_unit_id_placeholder' => 'Select Organization Unit',
            'job_type_id' => 'Job Type',
            'job_type_id_placeholder' => 'Select Job Type',
            'is_active' => 'Is Active',
        ],

        'label' => 'Employees',
        'create' => 'Create Employee',
        'edit' => 'Edit Employee',
        'view' => 'View Employee',
    ],

    'perspective_resource' => [
        'fields' => [
            'name' => 'Name',
            'description' => 'Description',
            'weight' => 'Weight',
            'is_active' => 'Is Active',
        ],

        'label' => 'Perspectives',
        'create' => 'Create Perspective',
        'edit' => 'Edit Perspective',
        'view' => 'View Perspective',
    ],

    'strategic_objective_resource' => [
        'fields' => [
            'strategic_objective' => 'Strategic Objective',
            'description' => 'Description',
            'perspective' => 'Perspective',
            'weight' => 'Weight',
            'number_of_year' => 'Number of Year',
            'start_date' => 'Start Date',
            'strategy_actions' => 'Strategy Actions',
            'end_date' => 'End Date',
            'expected_results' => 'Expected Results',
            'is_approved' => 'Is Approved',
            'approved_by' => 'Approved By',
            'employee' => 'Employee',
            'is_active' => 'Is Active',
        ],

        'label' => 'Strategic Objectives',
        'create' => 'Create Strategic Objective',
        'edit' => 'Edit Strategic Objective',
        'view' => 'View Strategic Objective',
    ],

];
