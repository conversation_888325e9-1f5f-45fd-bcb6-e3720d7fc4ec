<?php

return [

    'main' => [
        'dashboard' => 'Dashboard',
        'organo_structure_group' => 'Organization Structure',
        'role_manager_group' => 'Role Manager',
        'plan_group' => 'Plan',
        'user_management_group' => 'User Management',
    ],

    'role resource' => [
        'fields' => [
            'name' => 'Name',
            'description' => 'Description',
            'guard_name' => 'Guard Name',
            'permissions_count' => 'Permissions',
            'updated_at' => 'Updated At',
        ],

        'label' => 'Roles',
        'create' => 'Create Role',
        'edit' => 'Edit Role',
        'view' => 'View Role',
    ],

    'user resource' => [
        'fields' => [
            'employee_id' => 'Employee',
            'name' => 'Name',
            'email' => 'Email',
            'email_verified_at' => 'Email Verified At',
            'password' => 'Password',
            'roles' => 'Roles',
            'id' => 'ID',
        ],

        'label' => 'Users',
        'create' => 'Create User',
        'edit' => 'Edit User',
        'view' => 'View User',
    ],

    'unit_type_resource' => [
        'fields' => [
            'name' => 'Name',
            'hirarchy_level' => 'Hirarchy Level',
        ],

        'label' => 'Unit Types',
        'create' => 'Create Unit Type',
        'edit' => 'Edit Unit Type',
        'view' => 'View Unit Type',
    ],

    'organization_unit_resource' => [
        'fields' => [
            'name' => 'Name',
            'accronym' => 'Acronym',
            'unit_type_id' => 'Unit Type',
            'unit_type_id_placeholder' => 'Select Unit Type',
            'parent_id' => 'Parent',
            'parent_id_placeholder' => 'Select Parent',
            'is_active' => 'Is Active',
            'is_root' => 'Is Root',
            'is_last_child' => 'Is Last Child',
        ],

        'label' => 'Organization Units',
        'create' => 'Create Organization Unit',
        'edit' => 'Edit Organization Unit',
        'view' => 'View Organization Unit',
    ],

    'job_title_category_resource' => [
        'fields' => [
            'name' => 'Name',
            'description' => 'Description',
            'parent_id' => 'Parent',
            'parent_id_placeholder' => 'Select Parent',
            'code' => 'Code',
            'is_active' => 'Is Active',
        ],

        'label' => 'Job Title Categories',
        'create' => 'Create Job Title Category',
        'edit' => 'Edit Job Title Category',
        'view' => 'View Job Title Category',
    ],

    'job_type_resource' => [
        'fields' => [
            'name' => 'Name',
            'description' => 'Description',
        ],

        'label' => 'Job Types',
        'create' => 'Create Job Type',
        'edit' => 'Edit Job Type',
        'view' => 'View Job Type',
    ],

    'job_position_resource' => [
        'fields' => [
            'name' => 'Name',
            'description' => 'Description',
            'parent_id' => 'Parent',
            'code' => 'Code',
            'parent_id_placeholder' => 'Select Parent',
            'job_title_category_id' => 'Job Title Category',
            'job_title_category_id_placeholder' => 'Select Job Title Category',
            'job_type_id' => 'Job Type',
            'job_type_id_placeholder' => 'Select Job Type',
            'organization_unit_id' => 'Organization Unit',
            'organization_unit_id_placeholder' => 'Select Organization Unit',
            'is_active' => 'Is Active',
        ],

        'label' => 'Job Positions',
        'create' => 'Create Job Position',
        'edit' => 'Edit Job Position',
        'view' => 'View Job Position',
    ],

    'employee_resource' => [
        'fields' => [
            'name' => 'Name',
            'sex' => 'Sex',
            'sex_male' => 'Male',
            'sex_female' => 'Female',
            'job_position_date' => 'Job Position Date',
            'job_position_id' => 'Job Position',
            'job_position_id_placeholder' => 'Select Job Position',
            'organization_unit_id' => 'Organization Unit',
            'organization_unit_id_placeholder' => 'Select Organization Unit',
            'job_type_id' => 'Job Type',
            'job_type_id_placeholder' => 'Select Job Type',
            'is_active' => 'Is Active',
        ],

        'label' => 'Employees',
        'create' => 'Create Employee',
        'edit' => 'Edit Employee',
        'view' => 'View Employee',
    ],

    'perspective_resource' => [
        'fields' => [
            'name' => 'Name',
            'description' => 'Description',
            'weight' => 'Weight',
            'is_active' => 'Is Active',
        ],

        'label' => 'Perspectives',
        'create' => 'Create Perspective',
        'edit' => 'Edit Perspective',
        'view' => 'View Perspective',
    ],

    'strategic_objective_resource' => [
        'fields' => [
            'id' => 'ID',
            'strategic_objective' => 'Strategic Objective',
            'description' => 'Description',
            'perspective' => 'Perspective',
            'weight' => 'Weight',
            'number_of_year' => 'Number of Year',
            'start_date' => 'Start Date',
            'strategy_actions' => 'Strategy Actions',
            'end_date' => 'End Date',
            'expected_results' => 'Expected Results',
            'is_approved' => 'Is Approved',
            'approved_by' => 'Approved By',
            'employee' => 'Employee',
            'is_active' => 'Is Active',
        ],

        'sections' => [
            'basic_info' => 'Basic Information',
            'timeline' => 'Timeline',
            'details' => 'Details',
            'created_by' => 'Created By',
            'approved_by' => 'Approved By',
        ],

        'label' => 'Strategic Objectives',
        'create' => 'Create Strategic Objective',
        'edit' => 'Edit Strategic Objective',
        'view' => 'View Strategic Objective',
    ],

    'half_year_resource' => [
        'fields' => [
            'name' => 'Name',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
        ],

        'label' => 'Half Year',
        'create' => 'Create Half Year',
        'edit' => 'Edit Half Year',
        'view' => 'View Half Year',
    ],

    'quarter_resource' => [
        'fields' => [
            'name' => 'Name',
            'half_year_id' => 'Half Year',
            'half_year_id_placeholder' => 'Select Half Year',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
        ],

        'label' => 'Quarter',
        'create' => 'Create Quarter',
        'edit' => 'Edit Quarter',
        'view' => 'View Quarter',
    ],

    'month_resource' => [
        'fields' => [
            'name' => 'Name',
            'half_year_id' => 'Half Year',
            'half_year_id_placeholder' => 'Select Half Year',
            'quarter_id' => 'Quarter',
            'quarter_id_placeholder' => 'Select Quarter',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
        ],

        'label' => 'Month',
        'create' => 'Create Month',
        'edit' => 'Edit Month',
        'view' => 'View Month',
    ],

    'frequency_type_resource' => [
        'fields' => [
            'name' => 'Name',
            'column_name' => 'Column Name',
            'column_name_year' => 'Year',
            'column_name_half_year' => 'Half Year',
            'column_name_quarter' => 'Quarter',
            'column_name_month' => 'Month',
        ],

        'label' => 'Frequency Type',
        'create' => 'Create Frequency Type',
        'edit' => 'Edit Frequency Type',
        'view' => 'View Frequency Type',
    ],

    'kpi_resource' => [
        'fields' => [
            'name' => 'Name',
            'description' => 'Description',
            'total_target' => 'Total Target',
            'parent_kpi_id' => 'Parent KPI',
            'parent_kpi_id_placeholder' => 'Select Parent KPI',
            'parent_kpi_node_id' => 'Parent KPI Node',
            'parent_kpi_node_id_placeholder' => 'Select Parent KPI Node',
            'organization_unit_id' => 'Organization Unit',
            'organization_unit_id_placeholder' => 'Select Organization Unit',
            'strategic_objective_id' => 'Strategic Objective',
            'strategic_objective_id_placeholder' => 'Select Strategic Objective',
            'frequency_type_id' => 'Frequency Type',
            'frequency_type_id_placeholder' => 'Select Frequency Type',
            'unit' => 'Unit',
            'weight' => 'Weight',
            'number_of_year' => 'Number of Year',
            'start_date' => 'Start Date',
            'end_date' => 'End Date',
            'is_approved' => 'Is Approved',
            'approved_by' => 'Approved By',
            'is_active' => 'Is Active',
        ],

        'label' => 'KPIs',
        'create' => 'Create KPI',
        'edit' => 'Edit KPI',
        'view' => 'View KPI',
    ],

    'target_resource' => [
        'fields' => [
            'target' => 'Target',
            'description' => 'Description',
            'year' => 'Year',
            'month_id' => 'Month',
            'month_id_placeholder' => 'Select Month',
            'quarter_id' => 'Quarter',
            'quarter_id_placeholder' => 'Select Quarter',
            'half_year_id' => 'Half Year',
            'half_year_id_placeholder' => 'Select Half Year',
            'kpi_id' => 'KPI',
            'kpi_id_placeholder' => 'Select KPI',
            'user_id' => 'User',
            'user_id_placeholder' => 'Select User',
            'is_approved' => 'Is Approved',
            'approved_by' => 'Approved By',
            'is_active' => 'Is Active',
        ],

        'label' => 'Targets',
        'create' => 'Create Target',
        'edit' => 'Edit Target',
        'view' => 'View Target',
    ],

    'strategic_objective_assignment_resource' => [
        'fields' => [
            'strategic_objective_id' => 'Strategic Objective',
            'strategic_objective' => 'Strategic Objective',
            'strategic_objective_id_placeholder' => 'Select Strategic Objective',
            'strategic_objective_weight' => 'Strategic Objective Weight',
            'total_assignments' => 'Total Assignments',
            'total_weight' => 'Total Weight',
            'organization_unit' => 'Organization Unit',
            'organization_unit_id_placeholder' => 'Select Organization Unit',
            'assignment_weight' => 'Assignment Weight',
            'is_active' => 'Is Active',
        ],

        'actions' => [
            'save_for_unit' => 'Save for :unit',
            'confirm_save' => 'Confirm Save',
            'save_all_units' => 'Save for All Units',
            'confirm_save_all' => 'Confirm Save for All Units',
        ],

        'modals' => [
            'confirm_save_title' => 'Confirm Save for :unit',
            'confirm_save_description' => 'Are you sure you want to save the assignment for :unit?',
        ],

        'notifications' => [
            'created_success' => 'Assignment created successfully for :unit.',
            'updated_success' => 'Assignment updated successfully for :unit.',
            'incomplete_data' => 'Please complete the assignment for :unit before saving.',
        ],

        'label' => 'Strategic Objective Assignments',
        'create' => 'Create Strategic Objective Assignment',
        'edit' => 'Edit Strategic Objective Assignment',
        'view' => 'View Strategic Objective Assignment',
    ],

];
