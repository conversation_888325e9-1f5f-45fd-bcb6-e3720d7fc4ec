<?php

return [
    'user resource' => [
        'fields' => [
            'name' => 'Name',
            'email' => 'Email',
            'email_verified_at' => 'Email Verified At',
            'password' => 'Password',
            'roles' => 'Roles',
            'id' => 'ID',
        ],

        'label' => 'Users',
    ],

    'unit_type_resource' => [
        'fields' => [
            'name' => 'Name',
            'hirarchy_level' => 'Hirarchy Level',
        ],

        'label' => 'Unit Types',
        'create' => 'Create Unit Type',
        'edit' => 'Edit Unit Type',
        'view' => 'View Unit Type',
    ],

    'organization_unit_resource' => [
        'fields' => [
            'name' => 'Name',
            'accronym' => 'Accronym',
            'unit_type_id' => 'Unit Type',
            'parent_id' => 'Parent',
            'is_active' => 'Is Active',
            'is_root' => 'Is Root',
            'is_last_child' => 'Is Last Child',
        ],

        'label' => 'Organization Units',
        'create' => 'Create Organization Unit',
        'edit' => 'Edit Organization Unit',
        'view' => 'View Organization Unit',
    ],
];
