<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TargetResource\Pages;
use App\Filament\Resources\TargetResource\RelationManagers;
use App\Models\Target;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TargetResource extends Resource
{
    use Translatable;
    protected static ?string $model = Target::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = null;

    public static function getNavigationGroup(): ?string
    {
        return __('resource.main.plan_group');
    }

    public static function getNavigationBadge(): ?string
    {
        return strval(static::getEloquentQuery()->count());
    }

    public static function getNavigationLabel(): string
    {
        return __('resource.target_resource.label');
    }

    public static function getLabel(): string
    {
        return __('resource.target_resource.label');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('target')->label(__('resource.target_resource.fields.target')),
                Forms\Components\TextInput::make('description')->label(__('resource.target_resource.fields.description')),
                Forms\Components\DatePicker::make('year')->label(__('resource.target_resource.fields.year')),
                Forms\Components\Select::make('month_id')->relationship('month', 'name')->label(__('resource.target_resource.fields.month_id'))->searchable()->preload(),
                Forms\Components\Select::make('quarter_id')->relationship('quarter', 'name')->label(__('resource.target_resource.fields.quarter_id'))->searchable()->preload(),
                Forms\Components\Select::make('half_year_id')->relationship('halfYear', 'name')->label(__('resource.target_resource.fields.half_year_id'))->searchable()->preload(),
                Forms\Components\Select::make('kpi_id')->relationship('kpi', 'name')->label(__('resource.target_resource.fields.kpi_id'))->searchable()->preload(),
                Forms\Components\Select::make('user_id')->relationship('user', 'name')->label(__('resource.target_resource.fields.user_id'))->searchable()->preload(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('target')->label(__('resource.target_resource.fields.target')),
                Tables\Columns\TextColumn::make('description')->label(__('resource.target_resource.fields.description')),
                Tables\Columns\TextColumn::make('year')->label(__('resource.target_resource.fields.year')),
                Tables\Columns\TextColumn::make('month.name')->label(__('resource.target_resource.fields.month_id')),
                Tables\Columns\TextColumn::make('quarter.name')->label(__('resource.target_resource.fields.quarter_id')),
                Tables\Columns\TextColumn::make('halfYear.name')->label(__('resource.target_resource.fields.half_year_id')),
                Tables\Columns\TextColumn::make('kpi.name')->label(__('resource.target_resource.fields.kpi_id')),
                Tables\Columns\TextColumn::make('user.name')->label(__('resource.target_resource.fields.user_id')),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTargets::route('/'),
            'create' => Pages\CreateTarget::route('/create'),
            'edit' => Pages\EditTarget::route('/{record}/edit'),
        ];
    }
}
