<?php

namespace App\Filament\Resources;

use App\Filament\Resources\JobTitleCategoryResource\Pages;
use App\Filament\Resources\JobTitleCategoryResource\RelationManagers;
use App\Models\JobTitleCategory;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;


class JobTitleCategoryResource extends Resource
{
    use Translatable;
    protected static ?string $model = JobTitleCategory::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')->label(__('resource.job_title_category_resource.fields.name')),
                Forms\Components\TextInput::make('description')->label(__('resource.job_title_category_resource.fields.description')),
                Forms\Components\Select::make('parent_id')->relationship('parent', 'name')->label(__('resource.job_title_category_resource.fields.parent_id'))->placeholder(__('resource.job_title_category_resource.fields.parent_id_placeholder')),
                Forms\Components\TextInput::make('code')->label(__('resource.job_title_category_resource.fields.code')),
                Forms\Components\Toggle::make('is_active')->label(__('resource.job_title_category_resource.fields.is_active')),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->label(__('resource.job_title_category_resource.fields.name')),
                Tables\Columns\TextColumn::make('description')->label(__('resource.job_title_category_resource.fields.description')),
                Tables\Columns\TextColumn::make('parent.name')->label(__('resource.job_title_category_resource.fields.parent_id')),
                Tables\Columns\TextColumn::make('code')->label(__('resource.job_title_category_resource.fields.code')),
            ])
            ->filters([

            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListJobTitleCategories::route('/'),
            'create' => Pages\CreateJobTitleCategory::route('/create'),
            'edit' => Pages\EditJobTitleCategory::route('/{record}/edit'),
        ];
    }
}
