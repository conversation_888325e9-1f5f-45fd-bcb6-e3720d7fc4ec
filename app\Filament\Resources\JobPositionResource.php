<?php

namespace App\Filament\Resources;

use App\Filament\Resources\JobPositionResource\Pages;
use App\Filament\Resources\JobPositionResource\RelationManagers;
use App\Models\JobPosition;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class JobPositionResource extends Resource
{
    use Translatable;
    protected static ?string $navigationGroup = null;

    public static function getNavigationGroup(): ?string
    {
        return __('resource.main.organo_structure_group');
    }
    protected static ?string $model = JobPosition::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getNavigationBadge(): ?string
    {
        return strval(static::getEloquentQuery()->count());
    }

    public static function getNavigationLabel(): string
    {
        return __('resource.job_position_resource.label');
    }

    public static function getLabel(): string
    {
        return __('resource.job_position_resource.label');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')->label(__('resource.job_position_resource.fields.name')),
                Forms\Components\TextInput::make('description')->label(__('resource.job_position_resource.fields.description')),
                Forms\Components\Select::make('parent_id')->relationship('parent', 'name')->label(__('resource.job_position_resource.fields.parent_id'))->placeholder(__('resource.job_position_resource.fields.parent_id_placeholder')),
                Forms\Components\Select::make('job_title_category_id')->relationship('jobTitleCategory', 'name')->label(__('resource.job_position_resource.fields.job_title_category_id'))->placeholder(__('resource.job_position_resource.fields.job_title_category_id_placeholder')),
                Forms\Components\Select::make('job_type_id')->relationship('jobType', 'name')->label(__('resource.job_position_resource.fields.job_type_id'))->placeholder(__('resource.job_position_resource.fields.job_type_id_placeholder')),
                Forms\Components\Select::make('organization_unit_id')->relationship('organizationUnit', 'name')->label(__('resource.job_position_resource.fields.organization_unit_id'))->placeholder(__('resource.job_position_resource.fields.organization_unit_id_placeholder')),
                Forms\Components\Toggle::make('is_active')->label(__('resource.job_position_resource.fields.is_active')),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->label(__('resource.job_position_resource.fields.name')),
                Tables\Columns\TextColumn::make('description')->label(__('resource.job_position_resource.fields.description')),
                Tables\Columns\TextColumn::make('parent.name')->label(__('resource.job_position_resource.fields.parent_id')),
                Tables\Columns\TextColumn::make('jobTitleCategory.name')->label(__('resource.job_position_resource.fields.job_title_category_id')),
                Tables\Columns\TextColumn::make('jobType.name')->label(__('resource.job_position_resource.fields.job_type_id')),
                Tables\Columns\TextColumn::make('organizationUnit.name')->label(__('resource.job_position_resource.fields.organization_unit_id')),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListJobPositions::route('/'),
            'create' => Pages\CreateJobPosition::route('/create'),
            'edit' => Pages\EditJobPosition::route('/{record}/edit'),
        ];
    }
}
