<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FrequencyTypeResource\Pages;
use App\Filament\Resources\FrequencyTypeResource\RelationManagers;
use App\Models\FrequencyType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FrequencyTypeResource extends Resource
{
    use Translatable;
    protected static ?string $model = FrequencyType::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = null;

    public static function getNavigationGroup(): ?string
    {
        return __('resource.main.plan_group');
    }

    public static function getNavigationBadge(): ?string
    {
        return strval(static::getEloquentQuery()->count());
    }

    public static function getNavigationLabel(): string
    {
        return __('resource.frequency_type_resource.label');
    }

    public static function getLabel(): string
    {
        return __('resource.frequency_type_resource.label');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')->label(__('resource.frequency_type_resource.fields.name')),
                Forms\Components\TextInput::make('column_name')->label(__('resource.frequency_type_resource.fields.column_name')),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->label(__('resource.frequency_type_resource.fields.name')),
                Tables\Columns\TextColumn::make('column_name')->label(__('resource.frequency_type_resource.fields.column_name')),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFrequencyTypes::route('/'),
            'create' => Pages\CreateFrequencyType::route('/create'),
            'edit' => Pages\EditFrequencyType::route('/{record}/edit'),
        ];
    }
}
