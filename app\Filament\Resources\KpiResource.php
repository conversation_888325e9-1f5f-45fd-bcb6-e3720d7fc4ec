<?php

namespace App\Filament\Resources;

use App\Filament\Resources\KpiResource\Pages;
use App\Filament\Resources\KpiResource\RelationManagers;
use App\Models\Kpi;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class KpiResource extends Resource
{
    use Translatable;
    protected static ?string $model = Kpi::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = null;

    public static function getNavigationGroup(): ?string
    {
        return __('resource.main.plan_group');
    }

    public static function getNavigationBadge(): ?string
    {
        return strval(static::getEloquentQuery()->count());
    }

    public static function getNavigationLabel(): string
    {
        return __('resource.kpi_resource.label');
    }

    public static function getLabel(): string
    {
        return __('resource.kpi_resource.label');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')->label(__('resource.kpi_resource.fields.name')),
                Forms\Components\TextInput::make('description')->label(__('resource.kpi_resource.fields.description')),
                Forms\Components\TextInput::make('total_target')->label(__('resource.kpi_resource.fields.total_target')),
                Forms\Components\Select::make('parent_kpi_id')->relationship('parentKpi', 'name')->label(__('resource.kpi_resource.fields.parent_kpi_id'))->placeholder(__('resource.kpi_resource.fields.parent_kpi_id_placeholder')),
                Forms\Components\Select::make('parent_kpi_node_id')->relationship('parentKpiNode', 'name')->label(__('resource.kpi_resource.fields.parent_kpi_node_id'))->placeholder(__('resource.kpi_resource.fields.parent_kpi_node_id_placeholder')),
                Forms\Components\Select::make('organization_unit_id')->relationship('organizationUnit', 'name')->label(__('resource.kpi_resource.fields.organization_unit_id'))->placeholder(__('resource.kpi_resource.fields.organization_unit_id_placeholder')),
                Forms\Components\Select::make('strategic_objective_id')->relationship('strategicObjective', 'name')->label(__('resource.kpi_resource.fields.strategic_objective_id'))->placeholder(__('resource.kpi_resource.fields.strategic_objective_id_placeholder')),
                Forms\Components\Select::make('frequency_type_id')->relationship('frequencyType', 'name')->label(__('resource.kpi_resource.fields.frequency_type_id'))->placeholder(__('resource.kpi_resource.fields.frequency_type_id_placeholder')),
                Forms\Components\TextInput::make('unit')->label(__('resource.kpi_resource.fields.unit')),
                Forms\Components\TextInput::make('weight')->label(__('resource.kpi_resource.fields.weight')),
                Forms\Components\TextInput::make('number_of_year')->label(__('resource.kpi_resource.fields.number_of_year')),
                Forms\Components\DatePicker::make('start_date')->label(__('resource.kpi_resource.fields.start_date')),
                Forms\Components\DatePicker::make('end_date')->label(__('resource.kpi_resource.fields.end_date')),
                Forms\Components\Toggle::make('is_approved')->label(__('resource.kpi_resource.fields.is_approved')),
                Forms\Components\Toggle::make('is_active')->label(__('resource.kpi_resource.fields.is_active')),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->label(__('resource.kpi_resource.fields.name')),
                Tables\Columns\TextColumn::make('description')->label(__('resource.kpi_resource.fields.description')),
                Tables\Columns\TextColumn::make('total_target')->label(__('resource.kpi_resource.fields.total_target')),
                Tables\Columns\TextColumn::make('parentKpi.name')->label(__('resource.kpi_resource.fields.parent_kpi_id')),
                Tables\Columns\TextColumn::make('parentKpiNode.name')->label(__('resource.kpi_resource.fields.parent_kpi_node_id')),
                Tables\Columns\TextColumn::make('organizationUnit.name')->label(__('resource.kpi_resource.fields.organization_unit_id')),
                Tables\Columns\TextColumn::make('strategicObjective.name')->label(__('resource.kpi_resource.fields.strategic_objective_id')),
                Tables\Columns\TextColumn::make('frequencyType.name')->label(__('resource.kpi_resource.fields.frequency_type_id')),
                Tables\Columns\TextColumn::make('unit')->label(__('resource.kpi_resource.fields.unit')),
                Tables\Columns\TextColumn::make('weight')->label(__('resource.kpi_resource.fields.weight')),
                Tables\Columns\TextColumn::make('number_of_year')->label(__('resource.kpi_resource.fields.number_of_year')),
                Tables\Columns\TextColumn::make('start_date')->label(__('resource.kpi_resource.fields.start_date')),
                Tables\Columns\TextColumn::make('end_date')->label(__('resource.kpi_resource.fields.end_date')),
                Tables\Columns\IconColumn::make('is_approved')->label(__('resource.kpi_resource.fields.is_approved'))
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                Tables\Columns\IconColumn::make('is_active')->label(__('resource.kpi_resource.fields.is_active'))
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListKpis::route('/'),
            'create' => Pages\CreateKpi::route('/create'),
            'edit' => Pages\EditKpi::route('/{record}/edit'),
        ];
    }
}
