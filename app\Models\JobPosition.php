<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class JobPosition extends Model
{
    protected $fillable = [
        'name',
        'description',
        'parent_id',
        'job_title_category_id',
        'job_type_id',
        'organization_unit_id',
        'is_active',
    ];

    protected $casts = [
        'name' => 'array',
        'description' => 'array',
    ];

    public $translatable = ['name', 'description'];

    public function parent()
    {
        return $this->belongsTo(JobPosition::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(JobPosition::class, 'parent_id');
    }

    public function jobTitleCategory()
    {
        return $this->belongsTo(JobTitleCategory::class);
    }

    public function jobType()
    {
        return $this->belongsTo(JobType::class);
    }

    public function organizationUnit()
    {
        return $this->belongsTo(OrganizationUnit::class);
    }
}
