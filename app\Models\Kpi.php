<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Kpi extends Model
{
    use HasTranslations;

    protected $fillable = [
        'name',
        'description',
        'total_target',
        'parent_kpi_id',
        'parent_kpi_node_id',
        'organization_unit_id',
        'strategic_objective_id',
        'frequency_type_id',
        'unit',
        'weight',
        'number_of_year',
        'start_date',
        'end_date',
        'is_approved',
        'approved_by',
        'user_id',
        'is_active',
    ];

    protected $casts = [
        'name' => 'array',
        'description' => 'array',
        'unit' => 'array',
    ];

    public $translatable = ['name', 'description', 'unit'];

    public function parentKpi()
    {
        return $this->belongsTo(Kpi::class, 'parent_kpi_id');
    }

    public function parentKpiNode()
    {
        return $this->belongsTo(Kpi::class, 'parent_kpi_node_id');
    }

    public function organizationUnit()
    {
        return $this->belongsTo(OrganizationUnit::class);
    }

    public function strategicObjective()
    {
        return $this->belongsTo(StrategicObjective::class);
    }

    public function frequencyType()
    {
        return $this->belongsTo(FrequencyType::class);
    }

    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function employee()
    {
        return $this->belongsTo(Employee::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
